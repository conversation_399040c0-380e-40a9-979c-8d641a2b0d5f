const API_BASE_URL = 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
};

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'API request failed');
  }
  
  return data;
};

// Library API
export const libraryApi = {
  // Get all libraries for the current user
  getLibraries: async () => {
    const response = await fetch(`${API_BASE_URL}/libraries`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Get a specific library with its words
  getLibrary: async (libraryId: number) => {
    const response = await fetch(`${API_BASE_URL}/libraries/${libraryId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Create a new library
  createLibrary: async (libraryData: { name: string; description?: string }) => {
    const response = await fetch(`${API_BASE_URL}/libraries`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(libraryData),
    });
    return handleResponse(response);
  },

  // Update a library
  updateLibrary: async (libraryId: number, libraryData: { name: string; description?: string }) => {
    const response = await fetch(`${API_BASE_URL}/libraries/${libraryId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(libraryData),
    });
    return handleResponse(response);
  },

  // Delete a library
  deleteLibrary: async (libraryId: number) => {
    const response = await fetch(`${API_BASE_URL}/libraries/${libraryId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  },

  // Upload CSV to library
  uploadCSV: async (libraryId: number, file: File) => {
    const token = localStorage.getItem('access_token');
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/libraries/${libraryId}/upload-csv`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });
    return handleResponse(response);
  },
};

// Word API
export const wordApi = {
  // Add a word to a library
  addWord: async (wordData: {
    library_id: number;
    word: string;
    meaning: string;
    pronunciation?: string;
    synonym?: string;
    antonym?: string;
    example?: string;
    difficulty?: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/words`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(wordData),
    });
    return handleResponse(response);
  },

  // Update a word
  updateWord: async (wordId: number, wordData: {
    library_id: number;
    word: string;
    meaning: string;
    pronunciation?: string;
    synonym?: string;
    antonym?: string;
    example?: string;
    difficulty?: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/words/${wordId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(wordData),
    });
    return handleResponse(response);
  },

  // Remove a word from a library
  removeWord: async (wordId: number, libraryId: number) => {
    const response = await fetch(`${API_BASE_URL}/words/${wordId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
      body: JSON.stringify({ library_id: libraryId, word_id: wordId }),
    });
    return handleResponse(response);
  },

  // Mark word as learned
  markWordLearned: async (wordId: number, libraryId: number) => {
    const response = await fetch(`${API_BASE_URL}/words/${wordId}/learn`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ library_id: libraryId }),
    });
    return handleResponse(response);
  },

  // Mark word as unlearned
  markWordUnlearned: async (wordId: number, libraryId: number) => {
    const response = await fetch(`${API_BASE_URL}/words/${wordId}/unlearn`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ library_id: libraryId }),
    });
    return handleResponse(response);
  },
};

// Types for TypeScript
export interface Library {
  id: number;
  name: string;
  description?: string;
  is_master: boolean;
  word_count: number;
  learned_count: number;
  unlearned_count: number;
  created_at: string;
  updated_at: string;
  words?: Word[];
}

export interface Word {
  id: number;
  word: string;
  meaning: string;
  pronunciation?: string;
  synonym?: string;
  antonym?: string;
  example?: string;
  difficulty: string;
  is_learned: boolean;
  learned_at?: string;
  added_at: string;
  library_word_id: number;
  created_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  details?: any;
}
