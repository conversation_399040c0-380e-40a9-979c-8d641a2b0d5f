
import { useState, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogT<PERSON>le, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Upload, Plus, Edit, Trash2, BookOpen, Volume2, MoreHorizontal, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useLibrary } from "@/contexts/LibraryContext";
import { Library, Word } from "@/services/api";

const LibraryManager = () => {
  const {
    libraries,
    selectedLibrary,
    selectLibrary,
    isLoading,
    createLibrary,
    updateLibrary,
    deleteLibrary,
    addWord,
    updateWord,
    removeWord,
    markWordLearned,
    markWordUnlearned,
    uploadCSV
  } = useLibrary();

  // Local state for dialogs and forms
  const [showNewLibraryDialog, setShowNewLibraryDialog] = useState(false);
  const [showEditLibraryDialog, setShowEditLibraryDialog] = useState(false);
  const [showAddWordDialog, setShowAddWordDialog] = useState(false);
  const [showEditWordDialog, setShowEditWordDialog] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [libraryToEdit, setLibraryToEdit] = useState<Library | null>(null);
  const [wordToEdit, setWordToEdit] = useState<Word | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form states
  const [newLibraryForm, setNewLibraryForm] = useState({ name: '', description: '' });
  const [editLibraryForm, setEditLibraryForm] = useState({ name: '', description: '' });
  const [wordForm, setWordForm] = useState({
    word: '',
    meaning: '',
    pronunciation: '',
    synonym: '',
    antonym: '',
    example: '',
    difficulty: 'medium'
  });

  // Handler functions
  const handleCreateLibrary = async () => {
    if (!newLibraryForm.name.trim()) {
      toast.error('Library name is required');
      return;
    }

    const success = await createLibrary(newLibraryForm.name, newLibraryForm.description);
    if (success) {
      setNewLibraryForm({ name: '', description: '' });
      setShowNewLibraryDialog(false);
    }
  };

  const handleEditLibrary = async () => {
    if (!libraryToEdit || !editLibraryForm.name.trim()) {
      toast.error('Library name is required');
      return;
    }

    const success = await updateLibrary(libraryToEdit.id, editLibraryForm.name, editLibraryForm.description);
    if (success) {
      setEditLibraryForm({ name: '', description: '' });
      setLibraryToEdit(null);
      setShowEditLibraryDialog(false);
    }
  };

  const handleDeleteLibrary = async (library: Library) => {
    if (library.is_master) {
      toast.error('Cannot delete Master Library');
      return;
    }

    const success = await deleteLibrary(library.id);
    if (success) {
      // Library context will handle selecting a new library
    }
  };

  const handleAddWord = async () => {
    if (!wordForm.word.trim() || !wordForm.meaning.trim()) {
      toast.error('Word and meaning are required');
      return;
    }

    const success = await addWord(wordForm);
    if (success) {
      setWordForm({
        word: '',
        meaning: '',
        pronunciation: '',
        synonym: '',
        antonym: '',
        example: '',
        difficulty: 'medium'
      });
      setShowAddWordDialog(false);
    }
  };

  const handleCSVUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }

    const success = await uploadCSV(file);
    if (success) {
      setShowUploadDialog(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleWordAction = async (action: string, word: Word) => {
    switch (action) {
      case 'learn':
        await markWordLearned(word.id);
        break;
      case 'unlearn':
        await markWordUnlearned(word.id);
        break;
      case 'edit':
        setWordToEdit(word);
        setWordForm({
          word: word.word,
          meaning: word.meaning,
          pronunciation: word.pronunciation || '',
          synonym: word.synonym || '',
          antonym: word.antonym || '',
          example: word.example || '',
          difficulty: word.difficulty
        });
        setShowEditWordDialog(true);
        break;
      case 'delete':
        await removeWord(word.id);
        break;
      case 'pronounce':
        // Text-to-speech functionality
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(word.word);
          speechSynthesis.speak(utterance);
        } else {
          toast.info('Text-to-speech not supported in this browser');
        }
        break;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex gap-6">
        {/* Sidebar */}
        <div className="w-80 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                My Libraries
                <Dialog open={showNewLibraryDialog} onOpenChange={setShowNewLibraryDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <Plus className="h-4 w-4 mr-1" />
                      New
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New Library</DialogTitle>
                      <DialogDescription>
                        Create a new vocabulary library to organize your words.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="library-name">Library Name</Label>
                        <Input
                          id="library-name"
                          value={newLibraryForm.name}
                          onChange={(e) => setNewLibraryForm({ ...newLibraryForm, name: e.target.value })}
                          placeholder="Enter library name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="library-description">Description (Optional)</Label>
                        <Textarea
                          id="library-description"
                          value={newLibraryForm.description}
                          onChange={(e) => setNewLibraryForm({ ...newLibraryForm, description: e.target.value })}
                          placeholder="Enter library description"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setShowNewLibraryDialog(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateLibrary} disabled={isLoading}>
                          {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                          Create Library
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {libraries.map((library) => (
                <div
                  key={library.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedLibrary?.id === library.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => selectLibrary(library)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <BookOpen className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-sm">{library.name}</span>
                      {library.is_master && <Badge variant="secondary" className="text-xs">Master</Badge>}
                    </div>
                    {!library.is_master && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setLibraryToEdit(library);
                              setEditLibraryForm({ name: library.name, description: library.description || '' });
                              setShowEditLibraryDialog(true);
                            }}
                          >
                            <Edit className="h-3 w-3 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteLibrary(library);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-3 w-3 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                  <div className="text-xs text-gray-600">
                    {library.word_count} words • {library.learned_count} learned
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                    <div
                      className="bg-blue-600 h-1 rounded-full"
                      style={{
                        width: `${library.word_count > 0 ? (library.learned_count / library.word_count) * 100 : 0}%`
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{selectedLibrary?.name || 'No Library Selected'}</CardTitle>
                  <CardDescription>Manage your vocabulary collection</CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
                    <DialogTrigger asChild>
                      <Button variant="outline" disabled={!selectedLibrary}>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload CSV
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Upload CSV File</DialogTitle>
                        <DialogDescription>
                          Upload a CSV file with columns: word, meaning, pronunciation, synonym, antonym, example, difficulty
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-2">Select your CSV file</p>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept=".csv"
                            onChange={handleCSVUpload}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            Browse Files
                          </Button>
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" onClick={() => setShowUploadDialog(false)}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <Dialog open={showAddWordDialog} onOpenChange={setShowAddWordDialog}>
                    <DialogTrigger asChild>
                      <Button disabled={!selectedLibrary}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Word
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Add New Word</DialogTitle>
                        <DialogDescription>
                          Add a new word to {selectedLibrary?.name}
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="word">Word *</Label>
                          <Input
                            id="word"
                            value={wordForm.word}
                            onChange={(e) => setWordForm({ ...wordForm, word: e.target.value })}
                            placeholder="Enter word"
                          />
                        </div>
                        <div>
                          <Label htmlFor="meaning">Meaning *</Label>
                          <Textarea
                            id="meaning"
                            value={wordForm.meaning}
                            onChange={(e) => setWordForm({ ...wordForm, meaning: e.target.value })}
                            placeholder="Enter meaning"
                          />
                        </div>
                        <div>
                          <Label htmlFor="pronunciation">Pronunciation</Label>
                          <Input
                            id="pronunciation"
                            value={wordForm.pronunciation}
                            onChange={(e) => setWordForm({ ...wordForm, pronunciation: e.target.value })}
                            placeholder="e.g., /ˈwɜrd/"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="synonym">Synonym</Label>
                            <Input
                              id="synonym"
                              value={wordForm.synonym}
                              onChange={(e) => setWordForm({ ...wordForm, synonym: e.target.value })}
                              placeholder="Synonym"
                            />
                          </div>
                          <div>
                            <Label htmlFor="antonym">Antonym</Label>
                            <Input
                              id="antonym"
                              value={wordForm.antonym}
                              onChange={(e) => setWordForm({ ...wordForm, antonym: e.target.value })}
                              placeholder="Antonym"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="example">Example</Label>
                          <Textarea
                            id="example"
                            value={wordForm.example}
                            onChange={(e) => setWordForm({ ...wordForm, example: e.target.value })}
                            placeholder="Example sentence"
                          />
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" onClick={() => setShowAddWordDialog(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleAddWord} disabled={isLoading}>
                            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                            Add Word
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {!selectedLibrary ? (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Select a library to view and manage words</p>
                </div>
              ) : (
                <Tabs defaultValue="table" className="w-full">
                  <TabsList>
                    <TabsTrigger value="table">Table View</TabsTrigger>
                    <TabsTrigger value="cards">Card View</TabsTrigger>
                  </TabsList>

                  <TabsContent value="table" className="space-y-4">
                    {selectedLibrary.words && selectedLibrary.words.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-3 font-medium">Word</th>
                              <th className="text-left p-3 font-medium">Status</th>
                              <th className="text-left p-3 font-medium">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedLibrary.words.map((word: Word) => (
                              <tr key={word.id} className="border-b hover:bg-gray-50">
                                <td className="p-3">
                                  <div className="flex items-center space-x-2">
                                    <span className="font-medium capitalize">{word.word}</span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="p-1 h-6 w-6"
                                      onClick={() => handleWordAction("pronounce", word)}
                                      title="Pronounce word"
                                    >
                                      <Volume2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </td>
                                <td className="p-3">
                                  <Badge variant={word.is_learned ? "default" : "secondary"}>
                                    {word.is_learned ? "Learned" : "Unlearned"}
                                  </Badge>
                                </td>
                                <td className="p-3">
                                  <div className="flex space-x-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleWordAction(word.is_learned ? "unlearn" : "learn", word)}
                                      title={word.is_learned ? "Mark as unlearned" : "Mark as learned"}
                                      className="text-green-600 hover:text-green-700"
                                    >
                                      {word.is_learned ? "✓" : "○"}
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleWordAction("edit", word)}
                                      title="Edit word"
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleWordAction("delete", word)}
                                      className="text-red-600 hover:text-red-700"
                                      title="Delete word"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 mb-4">No words in this library yet</p>
                        <Button onClick={() => setShowAddWordDialog(true)}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Your First Word
                        </Button>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="cards" className="space-y-4">
                    {selectedLibrary.words && selectedLibrary.words.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {selectedLibrary.words.map((word: Word) => (
                          <Card key={word.id} className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-lg capitalize truncate" title={word.word}>
                                  {word.word}
                                </CardTitle>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-1 h-6 w-6 flex-shrink-0"
                                  onClick={() => handleWordAction("pronounce", word)}
                                  title="Pronounce word"
                                >
                                  <Volume2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className="flex flex-col space-y-3">
                                <Badge
                                  variant={word.is_learned ? "default" : "secondary"}
                                  className="w-fit"
                                >
                                  {word.is_learned ? "Learned" : "Unlearned"}
                                </Badge>

                                <div className="flex flex-col space-y-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleWordAction(word.is_learned ? "unlearn" : "learn", word)}
                                    className={word.is_learned ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
                                  >
                                    {word.is_learned ? "Mark Unlearned" : "Mark Learned"}
                                  </Button>

                                  <div className="flex space-x-1">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleWordAction("edit", word)}
                                      className="flex-1"
                                    >
                                      <Edit className="h-3 w-3 mr-1" />
                                      Edit
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleWordAction("delete", word)}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 mb-4">No words in this library yet</p>
                        <Button onClick={() => setShowAddWordDialog(true)}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Your First Word
                        </Button>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Edit Library Dialog */}
        <Dialog open={showEditLibraryDialog} onOpenChange={setShowEditLibraryDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Library</DialogTitle>
              <DialogDescription>
                Update the library name and description.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-library-name">Library Name</Label>
                <Input
                  id="edit-library-name"
                  value={editLibraryForm.name}
                  onChange={(e) => setEditLibraryForm({ ...editLibraryForm, name: e.target.value })}
                  placeholder="Enter library name"
                />
              </div>
              <div>
                <Label htmlFor="edit-library-description">Description (Optional)</Label>
                <Textarea
                  id="edit-library-description"
                  value={editLibraryForm.description}
                  onChange={(e) => setEditLibraryForm({ ...editLibraryForm, description: e.target.value })}
                  placeholder="Enter library description"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowEditLibraryDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditLibrary} disabled={isLoading}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Update Library
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Word Dialog */}
        <Dialog open={showEditWordDialog} onOpenChange={setShowEditWordDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Word</DialogTitle>
              <DialogDescription>
                Update the word details.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-word">Word *</Label>
                <Input
                  id="edit-word"
                  value={wordForm.word}
                  onChange={(e) => setWordForm({ ...wordForm, word: e.target.value })}
                  placeholder="Enter word"
                />
              </div>
              <div>
                <Label htmlFor="edit-meaning">Meaning *</Label>
                <Textarea
                  id="edit-meaning"
                  value={wordForm.meaning}
                  onChange={(e) => setWordForm({ ...wordForm, meaning: e.target.value })}
                  placeholder="Enter meaning"
                />
              </div>
              <div>
                <Label htmlFor="edit-pronunciation">Pronunciation</Label>
                <Input
                  id="edit-pronunciation"
                  value={wordForm.pronunciation}
                  onChange={(e) => setWordForm({ ...wordForm, pronunciation: e.target.value })}
                  placeholder="e.g., /ˈwɜrd/"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-synonym">Synonym</Label>
                  <Input
                    id="edit-synonym"
                    value={wordForm.synonym}
                    onChange={(e) => setWordForm({ ...wordForm, synonym: e.target.value })}
                    placeholder="Synonym"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-antonym">Antonym</Label>
                  <Input
                    id="edit-antonym"
                    value={wordForm.antonym}
                    onChange={(e) => setWordForm({ ...wordForm, antonym: e.target.value })}
                    placeholder="Antonym"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-example">Example</Label>
                <Textarea
                  id="edit-example"
                  value={wordForm.example}
                  onChange={(e) => setWordForm({ ...wordForm, example: e.target.value })}
                  placeholder="Example sentence"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowEditWordDialog(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={async () => {
                    if (wordToEdit) {
                      const success = await updateWord(wordToEdit.id, wordForm);
                      if (success) {
                        setShowEditWordDialog(false);
                        setWordToEdit(null);
                        setWordForm({
                          word: '',
                          meaning: '',
                          pronunciation: '',
                          synonym: '',
                          antonym: '',
                          example: '',
                          difficulty: 'medium'
                        });
                      }
                    }
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Update Word
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default LibraryManager;
