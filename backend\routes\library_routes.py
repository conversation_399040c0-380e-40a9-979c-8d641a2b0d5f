from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import ValidationError
from sqlalchemy.exc import IntegrityError
from models import User, Library, Word, LibraryWord, db
from schemas import LibrarySchema
from auth import token_required

library_bp = Blueprint('library', __name__, url_prefix='/api/libraries')

@library_bp.after_request
def after_request(response):
    """Add CORS headers to all responses"""
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@library_bp.route('', methods=['OPTIONS'])
@library_bp.route('/<int:library_id>', methods=['OPTIONS'])
@library_bp.route('/<int:library_id>/words', methods=['OPTIONS'])
def handle_options(library_id=None):
    """Handle preflight OPTIONS requests"""
    return jsonify({'success': True}), 200

@library_bp.route('', methods=['GET'])
@token_required
def get_libraries(current_user):
    """Get all libraries for the current user"""
    try:
        libraries = Library.query.filter_by(user_id=current_user.id).order_by(
            Library.is_master.desc(), Library.created_at.asc()
        ).all()

        return jsonify({
            'success': True,
            'data': {
                'libraries': [library.to_dict() for library in libraries]
            }
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Failed to fetch libraries',
            'details': str(e)
        }), 500

@library_bp.route('', methods=['POST'])
@token_required
def create_library(current_user):
    """Create a new library"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate input
        schema = LibrarySchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'error': 'Validation failed',
                'details': err.messages
            }), 400

        # Check if library name already exists for this user
        existing_library = Library.query.filter_by(
            user_id=current_user.id,
            name=validated_data['name']
        ).first()

        if existing_library:
            return jsonify({
                'success': False,
                'error': 'Library with this name already exists'
            }), 409

        # Create new library
        library = Library(
            user_id=current_user.id,
            name=validated_data['name'],
            description=validated_data.get('description'),
            is_master=False  # Only one master library per user, created during registration
        )

        db.session.add(library)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Library created successfully',
            'data': {
                'library': library.to_dict()
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'Failed to create library',
            'details': str(e)
        }), 500

@library_bp.route('/<int:library_id>', methods=['GET'])
@token_required
def get_library(current_user, library_id):
    """Get a specific library with its words"""
    try:
        library = Library.query.filter_by(
            id=library_id,
            user_id=current_user.id
        ).first()

        if not library:
            return jsonify({
                'success': False,
                'error': 'Library not found'
            }), 404

        # Get words in this library
        library_words = LibraryWord.query.filter_by(library_id=library_id).all()
        words_data = []

        for lw in library_words:
            word_dict = lw.word.to_dict()
            word_dict['is_learned'] = lw.is_learned
            word_dict['learned_at'] = lw.learned_at.isoformat() if lw.learned_at else None
            word_dict['added_at'] = lw.added_at.isoformat() if lw.added_at else None
            word_dict['library_word_id'] = lw.id
            words_data.append(word_dict)

        library_dict = library.to_dict()
        library_dict['words'] = words_data

        return jsonify({
            'success': True,
            'data': {
                'library': library_dict
            }
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Failed to fetch library',
            'details': str(e)
        }), 500

@library_bp.route('/<int:library_id>', methods=['PUT'])
@token_required
def update_library(current_user, library_id):
    """Update a library"""
    try:
        library = Library.query.filter_by(
            id=library_id,
            user_id=current_user.id
        ).first()

        if not library:
            return jsonify({
                'success': False,
                'error': 'Library not found'
            }), 404

        # Don't allow updating master library name
        if library.is_master:
            return jsonify({
                'success': False,
                'error': 'Cannot modify master library'
            }), 400

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        # Validate input
        schema = LibrarySchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as err:
            return jsonify({
                'success': False,
                'error': 'Validation failed',
                'details': err.messages
            }), 400

        # Check if new name conflicts with existing libraries
        if validated_data['name'] != library.name:
            existing_library = Library.query.filter_by(
                user_id=current_user.id,
                name=validated_data['name']
            ).first()

            if existing_library:
                return jsonify({
                    'success': False,
                    'error': 'Library with this name already exists'
                }), 409

        # Update library
        library.name = validated_data['name']
        library.description = validated_data.get('description')

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Library updated successfully',
            'data': {
                'library': library.to_dict()
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'Failed to update library',
            'details': str(e)
        }), 500

@library_bp.route('/<int:library_id>', methods=['DELETE'])
@token_required
def delete_library(current_user, library_id):
    """Delete a library"""
    try:
        library = Library.query.filter_by(
            id=library_id,
            user_id=current_user.id
        ).first()

        if not library:
            return jsonify({
                'success': False,
                'error': 'Library not found'
            }), 404

        # Don't allow deleting master library
        if library.is_master:
            return jsonify({
                'success': False,
                'error': 'Cannot delete master library'
            }), 400

        db.session.delete(library)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Library deleted successfully'
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'Failed to delete library',
            'details': str(e)
        }), 500

@library_bp.route('/<int:library_id>/upload-csv', methods=['POST'])
@token_required
def upload_csv_to_library(current_user, library_id):
    """Upload CSV file to add words to a library"""
    import csv
    import io

    try:
        # Verify library belongs to user
        library = Library.query.filter_by(
            id=library_id,
            user_id=current_user.id
        ).first()

        if not library:
            return jsonify({
                'success': False,
                'error': 'Library not found'
            }), 404

        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file uploaded'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400

        # Check file extension
        if not file.filename.lower().endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'File must be a CSV file'
            }), 400

        # Read and parse CSV
        try:
            stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
            csv_reader = csv.DictReader(stream)

            # Expected columns: word, meaning, pronunciation (optional), synonym (optional), antonym (optional), example (optional)
            required_columns = ['word', 'meaning']
            optional_columns = ['pronunciation', 'synonym', 'antonym', 'example', 'difficulty']

            # Check if required columns exist
            if not all(col in csv_reader.fieldnames for col in required_columns):
                return jsonify({
                    'success': False,
                    'error': f'CSV must contain columns: {", ".join(required_columns)}',
                    'details': f'Found columns: {", ".join(csv_reader.fieldnames or [])}'
                }), 400

            words_added = 0
            words_skipped = 0
            errors = []

            # Get master library for auto-sync
            master_library = Library.query.filter_by(
                user_id=current_user.id,
                is_master=True
            ).first()

            for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 because row 1 is headers
                try:
                    word_text = row.get('word', '').strip().lower()
                    meaning_text = row.get('meaning', '').strip()

                    if not word_text or not meaning_text:
                        errors.append(f'Row {row_num}: Word and meaning are required')
                        continue

                    # Check if word already exists
                    existing_word = Word.query.filter_by(word=word_text).first()

                    if existing_word:
                        # Check if word is already in this library
                        existing_library_word = LibraryWord.query.filter_by(
                            library_id=library_id,
                            word_id=existing_word.id
                        ).first()

                        if existing_library_word:
                            words_skipped += 1
                            continue

                        # Add existing word to library
                        library_word = LibraryWord(
                            library_id=library_id,
                            word_id=existing_word.id
                        )
                        word = existing_word
                    else:
                        # Create new word
                        word = Word(
                            word=word_text,
                            meaning=meaning_text,
                            pronunciation=row.get('pronunciation', '').strip() or None,
                            synonym=row.get('synonym', '').strip() or None,
                            antonym=row.get('antonym', '').strip() or None,
                            example=row.get('example', '').strip() or None,
                            difficulty=row.get('difficulty', 'medium').strip() or 'medium'
                        )
                        db.session.add(word)
                        db.session.flush()  # Get the word ID

                        # Add word to library
                        library_word = LibraryWord(
                            library_id=library_id,
                            word_id=word.id
                        )

                    db.session.add(library_word)

                    # If not adding to master library, also add to master library
                    if not library.is_master and master_library:
                        existing_master_word = LibraryWord.query.filter_by(
                            library_id=master_library.id,
                            word_id=word.id
                        ).first()

                        if not existing_master_word:
                            master_library_word = LibraryWord(
                                library_id=master_library.id,
                                word_id=word.id
                            )
                            db.session.add(master_library_word)

                    words_added += 1

                except Exception as e:
                    errors.append(f'Row {row_num}: {str(e)}')
                    continue

            db.session.commit()

            return jsonify({
                'success': True,
                'message': f'CSV processed successfully',
                'data': {
                    'words_added': words_added,
                    'words_skipped': words_skipped,
                    'errors': errors
                }
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'error': 'Failed to parse CSV file',
                'details': str(e)
            }), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'Failed to upload CSV',
            'details': str(e)
        }), 500
